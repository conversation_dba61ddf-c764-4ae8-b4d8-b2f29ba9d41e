{"name": "shipease-login", "version": "1.0.0", "description": "Shipease Login System with Bootstrap 5", "main": "index.html", "scripts": {"start": "live-server --port=3000 --open=/index.html", "dev": "live-server --port=3000 --open=/index.html --watch=assets/", "build": "echo 'Build process would go here'", "test": "echo 'Tests would go here'"}, "keywords": ["shipease", "login", "bootstrap", "logistics", "shipping"], "author": "Shipease Team", "license": "MIT", "devDependencies": {"live-server": "^1.2.2"}, "dependencies": {"bootstrap": "^5.3.2"}, "repository": {"type": "git", "url": "https://github.com/shipease/login-system.git"}, "bugs": {"url": "https://github.com/shipease/login-system/issues"}, "homepage": "https://shipease.com"}