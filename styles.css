/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    overflow: hidden;
}

/* Container Layout */
.container {
    display: flex;
    height: 100vh;
    width: 100vw;
    min-height: 100vh;
    position: relative;
}

/* Left Section - Logistics Background */
.left-section {
    flex: 1.2;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.85) 0%, rgba(37, 99, 235, 0.85) 50%, rgba(29, 78, 216, 0.85) 100%);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Fixed Rounded Logo Styles */
.logo {
    position: fixed;
    top: 30px;
    left: 30px;
    z-index: 100;
    display: flex;
    align-items: center;
    gap: 12px;
    background: #ffffff;
    padding: 16px 24px;
    border-radius: 50px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    trans
}

.logo:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
}

.logo-icon {
    position: relative;
    width: 28px;
    height: 28px;
    flex-shrink: 0;
}

.triangle {
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
}

.triangle.green {
    border-width: 0 14px 24px 14px;
    border-color: transparent transparent #10b981 transparent;
    top: 0;
    left: 0;
}

.triangle.red {
    border-width: 0 14px 24px 14px;
    border-color: transparent transparent #ef4444 transparent;
    top: 2px;
    left: 2px;
}

.logo-text {
    font-size: 20px;
    font-weight: 700;
    color: #1f2937;
    letter-spacing: 0.5px;
    font-family: 'Inter', sans-serif;
}

/* Background Elements */
.background-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.7;
}

/* Globe */
.globe {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
}

.globe-outline {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.globe-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background:
        linear-gradient(90deg, transparent 49%, rgba(255, 255, 255, 0.4) 50%, transparent 51%),
        linear-gradient(0deg, transparent 49%, rgba(255, 255, 255, 0.4) 50%, transparent 51%);
    background-size: 30px 30px;
}

/* Airplane */
.airplane {
    position: absolute;
    top: 45%;
    left: 30%;
    font-size: 48px;
    color: rgba(255, 255, 255, 0.8);
    animation: fly 8s linear infinite;
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

@keyframes fly {
    0% {
        transform: translateX(-100px) rotate(15deg);
    }

    100% {
        transform: translateX(400px) rotate(15deg);
    }
}

/* Trucks */
.trucks {
    position: absolute;
    bottom: 25%;
    left: 20%;
}

.truck {
    position: absolute;
    font-size: 36px;
    color: rgba(255, 255, 255, 0.7);
    animation: drive 6s linear infinite;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.4));
}

.truck-1 {
    animation-delay: 0s;
}

.truck-2 {
    animation-delay: 2s;
}

.truck-3 {
    animation-delay: 4s;
}

@keyframes drive {
    0% {
        transform: translateX(-100px);
    }

    100% {
        transform: translateX(300px);
    }
}

/* Cargo Ship */
.cargo-ship {
    position: absolute;
    bottom: 15%;
    left: 10%;
    font-size: 42px;
    color: rgba(255, 255, 255, 0.6);
    animation: sail 10s linear infinite;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.4));
}

@keyframes sail {
    0% {
        transform: translateX(-50px);
    }

    100% {
        transform: translateX(200px);
    }
}

/* City Skyline */
.city-skyline {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 20%;
}

.building {
    position: absolute;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px 4px 0 0;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.building-1 {
    left: 10%;
    width: 40px;
    height: 80px;
}

.building-2 {
    left: 15%;
    width: 30px;
    height: 120px;
}

.building-3 {
    left: 22%;
    width: 35px;
    height: 100px;
}

.building-4 {
    left: 30%;
    width: 45px;
    height: 140px;
}

/* Route Lines */
.route-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.route-line {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    animation: flow 12s linear infinite;
    border-radius: 1px;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.line-1 {
    top: 30%;
    left: 20%;
    width: 200px;
    transform: rotate(15deg);
}

.line-2 {
    top: 50%;
    left: 10%;
    width: 250px;
    transform: rotate(-10deg);
    animation-delay: 4s;
}

.line-3 {
    top: 70%;
    left: 15%;
    width: 180px;
    transform: rotate(25deg);
    animation-delay: 8s;
}

@keyframes flow {
    0% {
        opacity: 0;
        transform: translateX(-100px);
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0;
        transform: translateX(100px);
    }
}

/* Right Section - Login Form */
.right-section {
    flex: 1;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    position: relative;
}

.right-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 0, 0, 0.02) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.02) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.3;
}

/* Form Container */
.form-container {
    background: #ffffff;
    padding: 35px 30px;
    border-radius: 16px;
    border: 2px solid #e5e7eb;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    width: 100%;
    max-width: 380px;
    position: relative;
    z-index: 10;
}

/* Form Title */
.form-title {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    text-align: center;
    margin-bottom: 30px;
    letter-spacing: -0.3px;
}

/* Input Groups */
.input-group {
    position: relative;
    margin-bottom: 18px;
}

.input-group input {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 400;
    color: #374151;
    background: #ffffff;
    transition: all 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.1);
}

.input-group input::placeholder {
    color: #9ca3af;
}

/* Password Toggle */
.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    color: #6b7280;
    cursor: pointer;
    font-size: 16px;
    padding: 6px 8px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: #374151;
    border-color: #9ca3af;
    background: #f9fafb;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #6b7280;
    cursor: pointer;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #9ca3af;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked+.checkmark {
    background: #6b7280;
    border-color: #6b7280;
}

.remember-me input[type="checkbox"]:checked+.checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    font-size: 14px;
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #2563eb;
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 14px 20px;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    border: 2px solid #3b82f6;
    border-radius: 8px;
    color: white;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 28px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
    border-color: #1e40af;
}

/* Social Login */
.social-login {
    text-align: center;
    margin-bottom: 24px;
}

.social-text {
    font-size: 13px;
    color: #6b7280;
    margin-bottom: 16px;
}

.social-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.social-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 12px 14px;
    border: 1.5px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    color: #374151;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 70px;
}

.social-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
}

.social-btn i {
    font-size: 20px;
}

.social-btn span {
    font-size: 12px;
}

/* Signup Link */
.signup-link {
    text-align: center;
}

.signup-link p {
    font-size: 14px;
    color: #6b7280;
}

.signup-text {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.signup-text:hover {
    color: #2563eb;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        flex-direction: column;
    }

    .left-section {
        flex: none;
        height: 40vh;
    }

    .right-section {
        flex: none;
        height: 60vh;
        padding: 20px;
    }

    .form-container {
        padding: 30px 25px;
        max-width: 400px;
    }

    .logo {
        top: 20px;
        left: 20px;
        padding: 14px 20px;
    }

    .logo-text {
        font-size: 18px;
    }

    .logo-icon {
        width: 24px;
        height: 24px;
    }

    .triangle.green {
        border-width: 0 12px 20px 12px;
    }

    .triangle.red {
        border-width: 0 12px 20px 12px;
        top: 1px;
        left: 1px;
    }

    .globe {
        width: 200px;
        height: 200px;
    }

    .airplane {
        font-size: 32px;
    }

    .truck {
        font-size: 24px;
    }

    .cargo-ship {
        font-size: 28px;
    }
}

@media (max-width: 768px) {
    .left-section {
        height: 35vh;
    }

    .right-section {
        height: 65vh;
        padding: 15px;
    }

    .form-container {
        padding: 25px 20px;
        max-width: 350px;
    }

    .form-title {
        font-size: 24px;
        margin-bottom: 30px;
    }

    .social-buttons {
        flex-direction: column;
        gap: 8px;
    }

    .social-btn {
        flex-direction: row;
        justify-content: center;
        min-width: auto;
    }

    .logo {
        padding: 12px 18px;
    }

    .logo-text {
        font-size: 16px;
    }

    .logo-icon {
        width: 22px;
        height: 22px;
    }

    .triangle.green {
        border-width: 0 11px 18px 11px;
    }

    .triangle.red {
        border-width: 0 11px 18px 11px;
        top: 1px;
        left: 1px;
    }

    .globe {
        width: 150px;
        height: 150px;
    }
}

@media (max-width: 480px) {
    .left-section {
        height: 30vh;
    }

    .right-section {
        height: 70vh;
        padding: 10px;
    }

    .form-container {
        padding: 20px 15px;
        max-width: 320px;
    }

    .form-title {
        font-size: 20px;
        margin-bottom: 25px;
    }

    .input-group input {
        padding: 14px 16px;
        font-size: 14px;
    }

    .login-btn {
        padding: 14px 20px;
        font-size: 14px;
    }

    .logo {
        top: 15px;
        left: 15px;
        padding: 10px 16px;
    }

    .logo-text {
        font-size: 15px;
    }

    .logo-icon {
        width: 20px;
        height: 20px;
    }

    .triangle.green {
        border-width: 0 10px 16px 10px;
    }

    .triangle.red {
        border-width: 0 10px 16px 10px;
        top: 1px;
        left: 1px;
    }

    .globe {
        width: 120px;
        height: 120px;
    }

    .airplane {
        font-size: 24px;
    }

    .truck {
        font-size: 20px;
    }

    .cargo-ship {
        font-size: 22px;
    }
}