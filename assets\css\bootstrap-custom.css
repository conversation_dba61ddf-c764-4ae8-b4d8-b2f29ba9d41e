/* Bootstrap Custom Variables */
:root {
    --bs-primary: #4f46e5;
    --bs-primary-rgb: 79, 70, 229;
    --bs-secondary: #6b7280;
    --bs-success: #10b981;
    --bs-danger: #ef4444;
    --bs-warning: #f59e0b;
    --bs-info: #3b82f6;
    --bs-light: #f8fafc;
    --bs-dark: #1f2937;
    --bs-border-radius: 0.75rem;
    --bs-border-radius-lg: 1rem;
}

/* Global Overrides */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Logistics Section */
.logistics-section {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

/* Form Section */
.form-section {
    background: #f8fafc;
    min-height: 100vh;
}

/* Logo Styles */
.logo-icon {
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.triangle {
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
}

.triangle.green {
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 20px solid #10b981;
    top: 2px;
    left: 5px;
}

.triangle.red {
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 15px solid #ef4444;
    top: 8px;
    right: 5px;
}

/* Custom Card Styles */
.card {
    border: none;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-radius: 1.5rem;
}

.card-body {
    padding: 3rem;
}

/* Form Controls */
.form-control {
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
}

.input-group-text {
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem 0 0 0.75rem;
    background-color: #f8fafc;
}

/* Button Styles */
.btn {
    border-radius: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #6366f1 100%);
    border: none;
    box-shadow: 0 4px 14px 0 rgba(79, 70, 229, 0.39);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #4338ca 0%, #5b21b6 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(79, 70, 229, 0.5);
}

.btn-outline-secondary:hover {
    background-color: #6b7280;
    border-color: #6b7280;
}

.btn-outline-danger:hover {
    background-color: #ef4444;
    border-color: #ef4444;
}

/* Social Buttons */
.social-buttons .btn {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
}

.social-buttons .btn i {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
}

/* Background Elements */
.background-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

/* Globe */
.globe {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
}

.globe-outline {
    width: 100%;
    height: 100%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    position: absolute;
    animation: rotate 20s linear infinite;
}

.globe-grid {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: absolute;
    background: 
        radial-gradient(circle at 30% 30%, transparent 20px, rgba(255, 255, 255, 0.1) 21px, rgba(255, 255, 255, 0.1) 22px, transparent 23px),
        radial-gradient(circle at 70% 70%, transparent 15px, rgba(255, 255, 255, 0.1) 16px, rgba(255, 255, 255, 0.1) 17px, transparent 18px);
    animation: rotate 30s linear infinite reverse;
}

/* Airplane */
.airplane {
    position: absolute;
    top: 25%;
    right: 15%;
    color: rgba(255, 255, 255, 0.8);
    font-size: 2rem;
    animation: fly 15s ease-in-out infinite;
}

/* Trucks */
.trucks {
    position: absolute;
    bottom: 20%;
    left: 10%;
}

.truck {
    position: absolute;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    animation: move 12s ease-in-out infinite;
}

.truck-1 { animation-delay: 0s; }
.truck-2 { animation-delay: 4s; left: 50px; }
.truck-3 { animation-delay: 8s; left: 100px; }

/* Cargo Ship */
.cargo-ship {
    position: absolute;
    bottom: 30%;
    right: 20%;
    color: rgba(255, 255, 255, 0.6);
    font-size: 2.5rem;
    animation: float 8s ease-in-out infinite;
}

/* City Skyline */
.city-skyline {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 150px;
    display: flex;
    align-items: end;
    justify-content: space-around;
    opacity: 0.3;
}

.building {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px 4px 0 0;
    animation: pulse 4s ease-in-out infinite;
}

.building-1 { width: 40px; height: 80px; animation-delay: 0s; }
.building-2 { width: 60px; height: 120px; animation-delay: 1s; }
.building-3 { width: 35px; height: 90px; animation-delay: 2s; }
.building-4 { width: 50px; height: 100px; animation-delay: 3s; }

/* Route Lines */
.route-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.route-line {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
}

.line-1 {
    width: 200px;
    height: 2px;
    top: 40%;
    left: 20%;
    transform: rotate(15deg);
    animation: glow 3s ease-in-out infinite;
}

.line-2 {
    width: 150px;
    height: 2px;
    top: 60%;
    right: 25%;
    transform: rotate(-20deg);
    animation: glow 3s ease-in-out infinite 1s;
}

.line-3 {
    width: 180px;
    height: 2px;
    bottom: 40%;
    left: 30%;
    transform: rotate(10deg);
    animation: glow 3s ease-in-out infinite 2s;
}

/* Animations */
@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes fly {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(50px) translateY(-20px); }
    50% { transform: translateX(100px) translateY(0); }
    75% { transform: translateX(50px) translateY(20px); }
}

@keyframes move {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(30px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

@keyframes glow {
    0%, 100% { opacity: 0.2; }
    50% { opacity: 0.8; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: 2rem;
    }
    
    .globe {
        width: 200px;
        height: 200px;
    }
    
    .city-skyline {
        height: 100px;
    }
    
    .building-1 { width: 30px; height: 60px; }
    .building-2 { width: 45px; height: 90px; }
    .building-3 { width: 25px; height: 70px; }
    .building-4 { width: 35px; height: 80px; }
}
