// Password Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const passwordToggle = document.querySelector('.password-toggle');
    const passwordInput = document.getElementById('password');
    
    if (passwordToggle && passwordInput) {
        passwordToggle.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = passwordToggle.querySelector('i');
            if (type === 'text') {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }
    
    // Form Validation
    const loginForm = document.querySelector('.login-form');
    const usernameInput = document.getElementById('username');
    
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Basic validation
            let isValid = true;
            
            if (!usernameInput.value.trim()) {
                showError(usernameInput, 'Username is required');
                isValid = false;
            } else {
                removeError(usernameInput);
            }
            
            if (!passwordInput.value.trim()) {
                showError(passwordInput, 'Password is required');
                isValid = false;
            } else {
                removeError(passwordInput);
            }
            
            if (isValid) {
                // Simulate login process
                const loginBtn = document.querySelector('.login-btn');
                const originalText = loginBtn.textContent;
                
                loginBtn.textContent = 'Logging in...';
                loginBtn.disabled = true;
                
                setTimeout(() => {
                    alert('Login functionality would be implemented here');
                    loginBtn.textContent = originalText;
                    loginBtn.disabled = false;
                }, 2000);
            }
        });
    }
    
    // Social Login Buttons
    const socialButtons = document.querySelectorAll('.social-btn');
    socialButtons.forEach(button => {
        button.addEventListener('click', function() {
            const type = this.classList.contains('phone') ? 'Phone' : 
                        this.classList.contains('google') ? 'Google' : 'Facebook';
            alert(`${type} login would be implemented here`);
        });
    });
    
    // Remember Me Checkbox
    const rememberCheckbox = document.getElementById('remember');
    if (rememberCheckbox) {
        rememberCheckbox.addEventListener('change', function() {
            console.log('Remember me:', this.checked);
        });
    }
    
    // Forgot Password Link
    const forgotPasswordLink = document.querySelector('.forgot-password');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', function(e) {
            e.preventDefault();
            alert('Forgot password functionality would be implemented here');
        });
    }
    
    // Signup Link
    const signupLink = document.querySelector('.signup-text');
    if (signupLink) {
        signupLink.addEventListener('click', function(e) {
            e.preventDefault();
            alert('Signup page would be implemented here');
        });
    }
});

// Error handling functions
function showError(input, message) {
    const inputGroup = input.closest('.input-group');
    let errorElement = inputGroup.querySelector('.error-message');
    
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        errorElement.style.color = '#ef4444';
        errorElement.style.fontSize = '12px';
        errorElement.style.marginTop = '4px';
        inputGroup.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
    input.style.borderColor = '#ef4444';
}

function removeError(input) {
    const inputGroup = input.closest('.input-group');
    const errorElement = inputGroup.querySelector('.error-message');
    
    if (errorElement) {
        errorElement.remove();
    }
    
    input.style.borderColor = '#e5e7eb';
}

// Add smooth animations for form elements
document.addEventListener('DOMContentLoaded', function() {
    const formElements = document.querySelectorAll('.input-group, .form-options, .login-btn, .social-login, .signup-link');
    
    formElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, 100 * (index + 1));
    });
});

// Add hover effects for social buttons
document.addEventListener('DOMContentLoaded', function() {
    const socialButtons = document.querySelectorAll('.social-btn');
    
    socialButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.05)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});

// Add focus effects for inputs
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
    
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
}); 