<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shipease - Sign Up</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/bootstrap-custom.css">
</head>
<body>
    <div class="container-fluid p-0 min-vh-100">
        <div class="row g-0 min-vh-100">
            <!-- Left Section - Logistics Background -->
            <div class="col-lg-8 col-md-7 logistics-section position-relative overflow-hidden">
                <!-- Logo -->
                <div class="position-absolute top-0 start-0 p-4 z-3">
                    <div class="logo d-flex align-items-center">
                        <div class="logo-icon me-3">
                            <div class="triangle green"></div>
                            <div class="triangle red"></div>
                        </div>
                        <span class="logo-text text-white fw-bold fs-4">SHIPEASE</span>
                    </div>
                </div>

                <!-- Background Elements -->
                <div class="background-elements">
                    <!-- Globe -->
                    <div class="globe">
                        <div class="globe-outline"></div>
                        <div class="globe-grid"></div>
                    </div>

                    <!-- Airplane -->
                    <div class="airplane">
                        <i class="fas fa-plane"></i>
                    </div>

                    <!-- Trucks -->
                    <div class="trucks">
                        <div class="truck truck-1">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="truck truck-2">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="truck truck-3">
                            <i class="fas fa-truck"></i>
                        </div>
                    </div>

                    <!-- Cargo Ship -->
                    <div class="cargo-ship">
                        <i class="fas fa-ship"></i>
                    </div>

                    <!-- City Skyline -->
                    <div class="city-skyline">
                        <div class="building building-1"></div>
                        <div class="building building-2"></div>
                        <div class="building building-3"></div>
                        <div class="building building-4"></div>
                    </div>

                    <!-- Route Lines -->
                    <div class="route-lines">
                        <div class="route-line line-1"></div>
                        <div class="route-line line-2"></div>
                        <div class="route-line line-3"></div>
                    </div>
                </div>
            </div>

            <!-- Right Section - Signup Form -->
            <div class="col-lg-4 col-md-5 form-section d-flex align-items-center justify-content-center bg-light">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-12 col-sm-10 col-md-12 col-lg-10">
                            <div class="card shadow-lg border-0 rounded-4">
                                <div class="card-body p-5">
                                    <div class="text-center mb-4">
                                        <h2 class="fw-bold text-dark mb-2">Create Account</h2>
                                        <p class="text-muted">Join Shipease today and start shipping!</p>
                                    </div>

                                    <form id="signupForm" class="needs-validation" novalidate>
                                        <!-- Full Name Field -->
                                        <div class="mb-3">
                                            <label for="fullName" class="form-label fw-medium">Full Name</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-light border-end-0">
                                                    <i class="fas fa-user text-muted"></i>
                                                </span>
                                                <input type="text" class="form-control border-start-0 ps-0" 
                                                       id="fullName" name="fullName" required>
                                                <div class="invalid-feedback">
                                                    Please enter your full name.
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Email Field -->
                                        <div class="mb-3">
                                            <label for="email" class="form-label fw-medium">Email Address</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-light border-end-0">
                                                    <i class="fas fa-envelope text-muted"></i>
                                                </span>
                                                <input type="email" class="form-control border-start-0 ps-0" 
                                                       id="email" name="email" required>
                                                <div class="invalid-feedback">
                                                    Please enter a valid email address.
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Username Field -->
                                        <div class="mb-3">
                                            <label for="username" class="form-label fw-medium">Username</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-light border-end-0">
                                                    <i class="fas fa-at text-muted"></i>
                                                </span>
                                                <input type="text" class="form-control border-start-0 ps-0" 
                                                       id="username" name="username" required>
                                                <div class="invalid-feedback">
                                                    Please choose a username.
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Password Field -->
                                        <div class="mb-3">
                                            <label for="password" class="form-label fw-medium">Password</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-light border-end-0">
                                                    <i class="fas fa-lock text-muted"></i>
                                                </span>
                                                <input type="password" class="form-control border-start-0 border-end-0 ps-0" 
                                                       id="password" name="password" required minlength="8">
                                                <button class="btn btn-outline-secondary border-start-0" type="button" 
                                                        id="togglePassword">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <div class="invalid-feedback">
                                                    Password must be at least 8 characters long.
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Terms & Conditions -->
                                        <div class="mb-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                                <label class="form-check-label text-muted" for="agreeTerms">
                                                    I agree to the <a href="#" class="text-primary">Terms & Conditions</a> 
                                                    and <a href="#" class="text-primary">Privacy Policy</a>
                                                </label>
                                                <div class="invalid-feedback">
                                                    You must agree to the terms and conditions.
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Signup Button -->
                                        <button type="submit" class="btn btn-primary w-100 py-3 fw-medium mb-4">
                                            <i class="fas fa-user-plus me-2"></i>Create Account
                                        </button>

                                        <!-- Login Link -->
                                        <div class="text-center">
                                            <p class="text-muted mb-0">
                                                Already have an account? 
                                                <a href="../index.html" class="text-primary text-decoration-none fw-medium">Login here</a>
                                            </p>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/bootstrap-main.js"></script>
</body>
</html>
